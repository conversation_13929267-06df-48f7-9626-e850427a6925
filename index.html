<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tic-Tac-Toe</title>
  <link rel="stylesheet" href="./src/style.css">
</head>
<body>
  <main>
    <!-- ℹ️ Info Button -->
  <button id="infoBtn">ℹ️</button>

  <!-- 📜 Instructions Modal -->
  <div id="infoModal" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2>How to Play</h2>
      <ul>
        <li>Game is played between two players: X and O.</li>
        <li>Take turns clicking on empty squares.</li>
        <li>First player to get 3 in a row (vertically, horizontally, or diagonally) wins!</li>
        <li>If all squares are filled and no one wins, it’s a draw.</li>
      </ul>
    </div>
  </div>

  <!-- 🎮 Landing Page -->
  <div class="landing" id="landingPage">
    <h1>Welcome to Tic <PERSON>c <PERSON></h1>
    <p>Simple. Classic. Fun.</p>
    <button id="startBtn">▶️ Play Game</button>
  </div>

  <!-- 🕹️ Game Page -->
  <div class="container hidden" id="gameContainer">
    <h1>Tic Tac Toe</h1>
    <div id="gameBoard" class="board"></div>
    <div class="info">
      <p id="status">Your turn: <span id="currentPlayer">X</span></p>
      <button id="resetBtn">🔁 Reset Game</button>
    </div>
  </div>

  </main>


  <script type="module" src="./src/main.js"></script>
</body>
</html>