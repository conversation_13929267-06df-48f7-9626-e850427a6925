<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tic-Tac-Toe</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      min-height: 100vh;
    }
  </style>
</head>
<body>
  <main>
    <!-- ℹ️ Info Button -->
  <button id="infoBtn">ℹ️</button>

  <!-- 📜 Instructions Modal -->
  <div id="infoModal" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2>How to Play</h2>
      <ul>
        <li>You play as X against the computer (O).</li>
        <li>Click on empty squares to make your move.</li>
        <li>The computer will automatically make its move after you.</li>
        <li>First to get 3 in a row (vertically, horizontally, or diagonally) wins!</li>
        <li>If all squares are filled and no one wins, it’s a draw.</li>
        <li>The computer uses smart AI - try to beat it!</li>
      </ul>
    </div>
  </div>

  <!-- 🎮 Landing Page -->
  <div class="landing" id="landingPage">
    <h1>Tic Tac Toe vs AI</h1>
    <p>Challenge the Computer. Can You Win?</p>
    <button id="startBtn">🎯 Challenge AI</button>
  </div>

  <!-- 🕹️ Game Page -->
  <div class="container hidden" id="gameContainer">
    <h1>You vs Computer</h1>
    <div class="game-info">
      <div class="player-info">
        <span class="player-label">You: X</span>
      </div>
      <div class="vs">VS</div>
      <div class="computer-info">
        <span class="computer-label">AI: O</span>
      </div>
    </div>
    <div id="gameBoard" class="board"></div>
    <div class="info">
      <p id="status">Your turn!</p>
      <button id="resetBtn">🔁 New Game</button>
    </div>
  </div>

  </main>

  <script src="./src/style.css"></script>

  <script type="module" src="./src/main.js"></script>
</body>
</html>