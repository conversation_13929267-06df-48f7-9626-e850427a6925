import '../src/style.css';
// Game elements
// 🎮 Elements
const board = document.getElementById('gameBoard');
const statusText = document.getElementById('status');
const currentPlayerEl = document.getElementById('currentPlayer');
const resetBtn = document.getElementById('resetBtn');
const startBtn = document.getElementById('startBtn');
const landingPage = document.getElementById('landingPage');
const gameContainer = document.getElementById('gameContainer');

// ℹ️ Modal Elements
const infoBtn = document.getElementById('infoBtn');
const infoModal = document.getElementById('infoModal');
const closeModal = document.querySelector('.close');

let currentPlayer = "X";
let gameActive = true;
let cells = Array(9).fill("");

const winPatterns = [
  [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows
  [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns
  [0, 4, 8], [2, 4, 6]             // diagonals
];

// 🎨 Render board
function drawBoard() {
  board.innerHTML = "";
  cells.forEach((val, idx) => {
    const cell = document.createElement("div");
    cell.classList.add("cell");
    cell.dataset.index = idx;
    cell.textContent = val;
    cell.addEventListener("click", handleCellClick);
    board.appendChild(cell);
  });
}

// 🖱️ Cell click handler
function handleCellClick(e) {
  const index = e.target.dataset.index;
  if (!gameActive || cells[index]) return;

  cells[index] = currentPlayer;
  drawBoard();
  checkWinner();
  if (gameActive) {
    currentPlayer = currentPlayer === "X" ? "O" : "X";
    currentPlayerEl.textContent = currentPlayer;
    statusText.innerHTML = `Your turn: <span id="currentPlayer">${currentPlayer}</span>`;
  }
}

// 🏆 Check win or draw
function checkWinner() {
  for (let pattern of winPatterns) {
    const [a, b, c] = pattern;
    if (cells[a] && cells[a] === cells[b] && cells[a] === cells[c]) {
      pattern.forEach(i => board.children[i].classList.add("winner"));
      statusText.textContent = `🎉 Player ${cells[a]} wins!`;
      gameActive = false;
      return;
    }
  }

  if (!cells.includes("")) {
    statusText.textContent = "It's a draw!";
    gameActive = false;
  }
}

// 🔁 Reset Game
resetBtn.addEventListener("click", () => {
  currentPlayer = "X";
  gameActive = true;
  cells = Array(9).fill("");
  statusText.innerHTML = `Your turn: <span id="currentPlayer">${currentPlayer}</span>`;
  drawBoard();
});

// ▶️ Start Game
startBtn.addEventListener("click", () => {
  landingPage.classList.add('hidden');
  gameContainer.classList.remove('hidden');
  drawBoard();
});

// ℹ️ Modal open/close
infoBtn.addEventListener("click", () => {
  infoModal.style.display = "block";
});

closeModal.addEventListener("click", () => {
  infoModal.style.display = "none";
});

window.addEventListener("click", (e) => {
  if (e.target === infoModal) {
    infoModal.style.display = "none";
  }
});
