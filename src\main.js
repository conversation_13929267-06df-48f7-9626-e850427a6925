import '../src/style.css';

// 🎮 Game Elements
const board = document.getElementById('gameBoard');
const statusText = document.getElementById('status');
const currentPlayerEl = document.getElementById('currentPlayer');
const resetBtn = document.getElementById('resetBtn');
const startBtn = document.getElementById('startBtn');
const landingPage = document.getElementById('landingPage');
const gameContainer = document.getElementById('gameContainer');

// ℹ️ Modal Elements
const infoBtn = document.getElementById('infoBtn');
const infoModal = document.getElementById('infoModal');
const closeModal = document.querySelector('.close');

// 🎯 Game State
let currentPlayer = "X"; // X = Human, O = Computer
let gameActive = true;
let cells = Array(9).fill("");
let isPlayerTurn = true;
let gameMode = "computer"; // computer vs human
let gameStats = {
  playerWins: 0,
  computerWins: 0,
  draws: 0
};

const winPatterns = [
  [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows
  [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns
  [0, 4, 8], [2, 4, 6]             // diagonals
];

// 🎵 Sound Effects (using Web Audio API)
const playSound = (type) => {
  const audioContext = new (window.AudioContext || window.webkitAudioContext)();
  let frequency;

  switch(type) {
    case 'click': frequency = 800; break;
    case 'win': frequency = 1200; break;
    case 'draw': frequency = 400; break;
    default: frequency = 600;
  }

  const oscillator = audioContext.createOscillator();
  const gainNode = audioContext.createGain();

  oscillator.connect(gainNode);
  gainNode.connect(audioContext.destination);

  oscillator.frequency.value = frequency;
  oscillator.type = 'sine';

  gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
  gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

  oscillator.start(audioContext.currentTime);
  oscillator.stop(audioContext.currentTime + 0.3);
};

// 🎨 Render board with animations
function drawBoard() {
  board.innerHTML = "";
  cells.forEach((val, idx) => {
    const cell = document.createElement("div");
    cell.classList.add("cell");
    cell.dataset.index = idx;
    cell.textContent = val;

    // Add filled class for animation if cell has value
    if (val) {
      cell.classList.add("filled");
      // Add color styling for X and O
      if (val === "X") {
        cell.style.color = "#667eea";
      } else {
        cell.style.color = "#764ba2";
      }
    }

    cell.addEventListener("click", handleCellClick);
    board.appendChild(cell);

    // Stagger animation for initial board render
    setTimeout(() => {
      cell.style.opacity = "1";
      cell.style.transform = "scale(1)";
    }, idx * 50);
  });
}

// 🖱️ Cell click handler - Human player move
function handleCellClick(e) {
  const index = parseInt(e.target.dataset.index);

  // Only allow human moves when it's player's turn and cell is empty
  if (!gameActive || cells[index] || !isPlayerTurn) return;

  // Play click sound
  try {
    playSound('click');
  } catch (error) {
    // Silently handle audio context errors
  }

  // Make human move
  makeMove(index, "X");

  // Check if game ended
  if (!checkWinner()) {
    // Computer's turn
    isPlayerTurn = false;
    updateStatus("Computer is thinking...");

    // Add delay for computer move to make it feel more natural
    setTimeout(() => {
      makeComputerMove();
    }, 800);
  }
}

// 🎯 Make a move (human or computer)
function makeMove(index, player) {
  cells[index] = player;

  // Add immediate visual feedback
  const cell = board.children[index];
  cell.style.transform = "scale(0.9)";
  setTimeout(() => {
    cell.style.transform = "scale(1)";
  }, 100);

  drawBoard();
}

// 🤖 Computer AI move using minimax algorithm
function makeComputerMove() {
  if (!gameActive) return;

  const bestMove = getBestMove();
  makeMove(bestMove, "O");

  if (!checkWinner()) {
    isPlayerTurn = true;
    updateStatus("Your turn!");
  }
}

// 🧠 Minimax algorithm for AI
function minimax(board, depth, isMaximizing) {
  const winner = checkWinnerForBoard(board);

  if (winner === "O") return 10 - depth; // Computer wins
  if (winner === "X") return depth - 10; // Human wins
  if (board.every(cell => cell !== "")) return 0; // Draw

  if (isMaximizing) {
    let bestScore = -Infinity;
    for (let i = 0; i < 9; i++) {
      if (board[i] === "") {
        board[i] = "O";
        const score = minimax(board, depth + 1, false);
        board[i] = "";
        bestScore = Math.max(score, bestScore);
      }
    }
    return bestScore;
  } else {
    let bestScore = Infinity;
    for (let i = 0; i < 9; i++) {
      if (board[i] === "") {
        board[i] = "X";
        const score = minimax(board, depth + 1, true);
        board[i] = "";
        bestScore = Math.min(score, bestScore);
      }
    }
    return bestScore;
  }
}

// 🎯 Get best move for computer
function getBestMove() {
  let bestScore = -Infinity;
  let bestMove = 0;

  for (let i = 0; i < 9; i++) {
    if (cells[i] === "") {
      cells[i] = "O";
      const score = minimax(cells, 0, false);
      cells[i] = "";

      if (score > bestScore) {
        bestScore = score;
        bestMove = i;
      }
    }
  }

  return bestMove;
}

// 🏆 Check winner for a given board state
function checkWinnerForBoard(board) {
  for (let pattern of winPatterns) {
    const [a, b, c] = pattern;
    if (board[a] && board[a] === board[b] && board[a] === board[c]) {
      return board[a];
    }
  }
  return null;
}

// 📝 Update status with animation
function updateStatus(message) {
  statusText.style.opacity = "0";
  statusText.style.transform = "translateY(-10px)";

  setTimeout(() => {
    statusText.innerHTML = message;
    statusText.style.opacity = "1";
    statusText.style.transform = "translateY(0)";
  }, 150);
}

// 🏆 Check win or draw with enhanced effects
function checkWinner() {
  const winner = checkWinnerForBoard(cells);

  if (winner) {
    // Find winning pattern
    for (let pattern of winPatterns) {
      const [a, b, c] = pattern;
      if (cells[a] === winner && cells[a] === cells[b] && cells[a] === cells[c]) {
        // Animate winning cells
        pattern.forEach((i, index) => {
          setTimeout(() => {
            board.children[i].classList.add("winner");
          }, index * 200);
        });
        break;
      }
    }

    // Play win sound
    try {
      setTimeout(() => playSound('win'), 600);
    } catch (error) {
      // Silently handle audio context errors
    }

    // Update status with celebration
    setTimeout(() => {
      if (winner === "X") {
        updateStatus(`🎉 You Win! 🏆`);
        gameStats.playerWins++;
      } else {
        updateStatus(`🤖 Computer Wins! 💻`);
        gameStats.computerWins++;
      }
      showCelebration();
    }, 800);

    gameActive = false;
    return true;
  }

  if (!cells.includes("")) {
    try {
      playSound('draw');
    } catch (error) {
      // Silently handle audio context errors
    }
    updateStatus("🤝 It's a draw!");
    gameStats.draws++;
    gameActive = false;
    return true;
  }

  return false;
}

// 🎊 Show celebration animation
function showCelebration() {
  const celebration = document.createElement('div');
  celebration.innerHTML = '🎉';
  celebration.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 4rem;
    z-index: 1000;
    animation: celebrationBounce 2s ease-out;
    pointer-events: none;
  `;

  document.body.appendChild(celebration);

  setTimeout(() => {
    celebration.remove();
  }, 2000);

  // Add celebration keyframes if not already present
  if (!document.querySelector('#celebration-styles')) {
    const style = document.createElement('style');
    style.id = 'celebration-styles';
    style.textContent = `
      @keyframes celebrationBounce {
        0% { transform: translate(-50%, -50%) scale(0) rotate(0deg); opacity: 0; }
        50% { transform: translate(-50%, -50%) scale(1.5) rotate(180deg); opacity: 1; }
        100% { transform: translate(-50%, -50%) scale(1) rotate(360deg); opacity: 0; }
      }
    `;
    document.head.appendChild(style);
  }
}

// 🔁 Reset Game with animation
resetBtn.addEventListener("click", () => {
  // Add reset animation
  board.style.transform = "scale(0.9)";
  board.style.opacity = "0.5";

  setTimeout(() => {
    currentPlayer = "X";
    gameActive = true;
    cells = Array(9).fill("");
    isPlayerTurn = true;
    updateStatus("Your turn!");

    board.style.transform = "scale(1)";
    board.style.opacity = "1";
    drawBoard();
  }, 200);
});

// ▶️ Start Game with smooth transition
startBtn.addEventListener("click", () => {
  startBtn.style.transform = "scale(0.95)";

  setTimeout(() => {
    landingPage.style.transform = "translateX(-100%)";
    landingPage.style.opacity = "0";

    setTimeout(() => {
      landingPage.classList.add('hidden');
      gameContainer.classList.remove('hidden');
      gameContainer.style.transform = "translateX(0)";
      gameContainer.style.opacity = "1";
      drawBoard();
    }, 500);
  }, 100);
});

// ℹ️ Modal open/close with animations
infoBtn.addEventListener("click", () => {
  infoModal.style.display = "block";
  // Trigger reflow to ensure display change is applied
  infoModal.offsetHeight;
  infoModal.style.animation = "modalFadeIn 0.3s ease-out";
});

closeModal.addEventListener("click", () => {
  infoModal.style.animation = "modalFadeIn 0.3s ease-out reverse";
  setTimeout(() => {
    infoModal.style.display = "none";
  }, 300);
});

window.addEventListener("click", (e) => {
  if (e.target === infoModal) {
    infoModal.style.animation = "modalFadeIn 0.3s ease-out reverse";
    setTimeout(() => {
      infoModal.style.display = "none";
    }, 300);
  }
});

// 🎮 Initialize game on page load
document.addEventListener('DOMContentLoaded', () => {
  // Add initial styles for smooth transitions
  gameContainer.style.transition = "all 0.5s cubic-bezier(0.4, 0, 0.2, 1)";
  landingPage.style.transition = "all 0.5s cubic-bezier(0.4, 0, 0.2, 1)";
  board.style.transition = "all 0.3s ease";
  statusText.style.transition = "all 0.3s ease";

  // Set initial status
  updateStatus("Ready to play against computer!");
});
