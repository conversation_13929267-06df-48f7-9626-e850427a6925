*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
html , body{
    height: 100%;
    width: 100%;
}
/* ===== Reset ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', sans-serif;
  background: linear-gradient(135deg, #1d1d1d, #3a3a3a);
  color: #fff;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* ===== ℹ️ Info Button ===== */
#infoBtn {
  position: fixed;
  top: 20px;
  left: 20px;
  font-size: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  padding: 10px;
  border-radius: 50%;
  cursor: pointer;
  color: #fff;
  transition: transform 0.3s ease;
  z-index: 10;
}
#infoBtn:hover {
  transform: rotate(20deg) scale(1.1);
}

/* ===== Modal ===== */
.modal {
  display: none;
  position: fixed;
  z-index: 999;
  left: 0; top: 0;
  width: 100%; height: 100%;
  background-color: rgba(0,0,0,0.6);
  backdrop-filter: blur(3px);
}

.modal-content {
  background: #222;
  color: white;
  padding: 20px;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  margin: 100px auto;
  animation: fadeIn 0.4s ease-in;
}

.modal-content ul {
  list-style: disc;
  padding-left: 20px;
}

.close {
  float: right;
  font-size: 24px;
  cursor: pointer;
}

/* ===== Landing Page ===== */
.landing {
  text-align: center;
  padding: 100px 20px;
  animation: fadeInUp 1s ease;
}

.landing h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.landing p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.8;
}

#startBtn {
  padding: 12px 25px;
  font-size: 1.1rem;
  background-color: #00b894;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}
#startBtn:hover {
  background-color: #00a383;
}

/* ===== Game Area ===== */
.container {
  text-align: center;
  padding: 20px;
  display: none;
}

.container h1 {
  margin-bottom: 20px;
}

.board {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-top: 20px;
}

.cell {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2.5rem;
  cursor: pointer;
  transition: 0.2s;
}

.cell:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.info {
  margin-top: 20px;
}

#resetBtn {
  padding: 10px 20px;
  font-size: 1rem;
  margin-top: 10px;
  border: none;
  border-radius: 8px;
  background: #ff6b6b;
  color: white;
  cursor: pointer;
}

#resetBtn:hover {
  background: #ff4c4c;
}

.winner {
  background-color: #4caf50 !important;
  animation: glow 0.8s infinite alternate;
}

/* Animations */
@keyframes glow {
  from { box-shadow: 0 0 10px #4caf50; }
  to { box-shadow: 0 0 20px #4caf50; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

.hidden {
  display: none;
}

/* Responsive */
@media (max-width: 480px) {
  .cell {
    height: 80px;
    font-size: 2rem;
  }
}
