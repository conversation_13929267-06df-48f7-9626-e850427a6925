/* ===== Reset & Base Styles ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* Animated background particles */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/></circle><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/></circle></svg>') repeat;
  pointer-events: none;
  z-index: -1;
}

/* ===== ℹ️ Info Button ===== */
#infoBtn {
  position: fixed;
  top: 20px;
  left: 20px;
  font-size: 24px;
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 12px;
  border-radius: 50%;
  cursor: pointer;
  color: #fff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#infoBtn:hover {
  transform: rotate(360deg) scale(1.15);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

#infoBtn:active {
  transform: scale(0.95);
}

/* ===== Modal ===== */
.modal {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  animation: modalFadeIn 0.3s ease-out;
}

.modal-content {
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  color: white;
  padding: 30px;
  border-radius: 20px;
  width: 90%;
  max-width: 450px;
  margin: 10% auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.modal-content h2 {
  margin-bottom: 20px;
  color: #667eea;
  font-size: 1.5rem;
}

.modal-content ul {
  list-style: none;
  padding-left: 0;
}

.modal-content li {
  padding: 8px 0;
  padding-left: 25px;
  position: relative;
}

.modal-content li::before {
  content: '✨';
  position: absolute;
  left: 0;
  top: 8px;
}

.close {
  position: absolute;
  right: 20px;
  top: 15px;
  font-size: 28px;
  cursor: pointer;
  color: #999;
  transition: all 0.3s ease;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(90deg);
}

/* ===== Landing Page ===== */
.landing {
  text-align: center;
  padding: 100px 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  animation: fadeInUp 1.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.landing h1 {
  font-size: 3.5rem;
  margin-bottom: 20px;
  background: linear-gradient(45deg, #fff, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleGlow 2s ease-in-out infinite alternate;
  text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.landing p {
  font-size: 1.4rem;
  margin-bottom: 40px;
  opacity: 0.9;
  animation: fadeInUp 1.5s ease 0.3s both;
}

#startBtn {
  padding: 15px 35px;
  font-size: 1.2rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 50px;
  cursor: pointer;
  color: white;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 1.8s ease 0.6s both;
}

#startBtn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

#startBtn:hover::before {
  left: 100%;
}

#startBtn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
}

#startBtn:active {
  transform: translateY(-1px) scale(1.02);
}

/* ===== Game Area ===== */
.container {
  text-align: center;
  padding: 40px 20px;
  display: none;
  min-height: 100vh;
  animation: gameSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.container h1 {
  margin-bottom: 20px;
  font-size: 2.5rem;
  background: linear-gradient(45deg, #fff, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== Game Info Section ===== */
.game-info {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  margin-bottom: 30px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.player-info, .computer-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 20px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
}

.player-label {
  font-size: 1.2rem;
  font-weight: bold;
  color: #667eea;
  text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.computer-label {
  font-size: 1.2rem;
  font-weight: bold;
  color: #764ba2;
  text-shadow: 0 0 10px rgba(118, 75, 162, 0.5);
}

.vs {
  font-size: 1.5rem;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
  animation: pulse 2s ease-in-out infinite alternate;
}

.board {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin: 30px auto;
  max-width: 350px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.cell {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2.8rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: scale(0.8);
}

.cell::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.cell:hover::before {
  transform: translateX(100%);
}

.cell:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.cell:active {
  transform: scale(0.95);
}

.cell.filled {
  animation: cellPop 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.info {
  margin-top: 30px;
}

.info p {
  font-size: 1.3rem;
  margin-bottom: 20px;
}

#currentPlayer {
  color: #667eea;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

#resetBtn {
  padding: 12px 25px;
  font-size: 1.1rem;
  margin-top: 15px;
  border: none;
  border-radius: 25px;
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

#resetBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

#resetBtn:active {
  transform: translateY(0);
}

.winner {
  background: linear-gradient(45deg, #4caf50, #45a049) !important;
  border-color: #4caf50 !important;
  animation: winnerGlow 1s ease-in-out infinite alternate;
  color: #fff !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

/* ===== Animations ===== */
@keyframes winnerGlow {
  from {
    box-shadow: 0 0 20px #4caf50, 0 0 40px #4caf50;
    transform: scale(1.05);
  }
  to {
    box-shadow: 0 0 30px #4caf50, 0 0 60px #4caf50;
    transform: scale(1.1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleGlow {
  from {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  to {
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.6), 0 0 40px rgba(102, 126, 234, 0.4);
  }
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes gameSlideIn {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes cellPop {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulse {
  from {
    transform: scale(1);
    opacity: 0.8;
  }
  to {
    transform: scale(1.1);
    opacity: 1;
  }
}

.hidden {
  display: none !important;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
  .landing h1 {
    font-size: 2.8rem;
  }

  .game-info {
    gap: 15px;
    flex-direction: column;
  }

  .vs {
    font-size: 1.2rem;
  }

  .board {
    max-width: 300px;
    gap: 10px;
  }

  .cell {
    height: 80px;
    font-size: 2.2rem;
  }
}

@media (max-width: 480px) {
  .landing {
    padding: 50px 15px;
  }

  .landing h1 {
    font-size: 2.2rem;
  }

  .landing p {
    font-size: 1.1rem;
  }

  .board {
    max-width: 280px;
    gap: 8px;
    padding: 15px;
  }

  .cell {
    height: 70px;
    font-size: 1.8rem;
  }

  .modal-content {
    margin: 20% auto;
    padding: 20px;
  }

  #infoBtn {
    top: 15px;
    left: 15px;
    font-size: 20px;
    padding: 10px;
  }
}
